'use server'
import { ChatHist<PERSON> } from '@/app/type/chat_history'
import { AdminPrismaMongoClient } from '@/lib/prisma'

export async function queryChatHistoryByChatId(chatId:string):Promise<ChatHistory[]> {
  const mongoClient = AdminPrismaMongoClient.getHaoguInstance()
  const history = await mongoClient.chat_history.findMany({ where:{ chat_id:chatId }, orderBy:[{ created_at:'asc' }] })
  return history as ChatHistory[]
}