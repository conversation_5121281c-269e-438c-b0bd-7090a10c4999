'use server'

import { Job, Queue } from 'bullmq'
import { RedisDB } from 'model/redis/redis'
import {
  getVisualizedSopQueueName,
  startTasks,
  VisualizedSopTasks
} from 'service/visualized_sop/visualized_sop_task_starter'
import {
  Action,
  ActionType,
  getSopTopicConditionRedisKey,
  getVisualizedRedisSopKey,
  ITask,
  LinkSourceType,
  Situation,
  TextType
} from 'service/visualized_sop/visualized_sop_type'
import { Config } from 'config'
import { getBotId, getUserId } from 'config/chat_id'
import { loadConfigByWxId } from 'model/bot_config/load_config'
import { AdminPrismaMongoClient } from '@/lib/prisma'
import { RedisCacheDB } from 'model/redis/redis_cache'
import { ScheduleTask } from 'service/visualized_sop/schedule'
import { getChatByCourseWeekRange } from './chat'
import { queryAccounts } from './account'
import { Sop } from '@/app/type/sop'
import { queryAllTags } from './sop_tag'
import { queryAllSopTopics } from './sop_topic'
import { sleep } from 'lib/schedule/schedule'
import { actionCustomMap, conditionJudgeMap, linkSourceVariableTagMap, textVariableMap } from 'haogu/src/visualized_sop/visualized_sop_variable'
import { chatStateStoreClient } from 'haogu/src/config/instance/base_instance'
import { calTaskTime } from 'haogu/src/visualized_sop/visualized_sop_processor'
import { haoguVisualizedSopProcessor } from 'haogu/src/config/instance/instance'
import { commonMessageSender } from 'haogu/src/config/instance/send_message_instance'

export async function queryExistSopByChatId(chatId:string):Promise<ITask[]> {
  const queue = new Queue<ITask>(getVisualizedSopQueueName('haogu', chatId.split('_')[1]), {
    connection: RedisDB.getInstance()
  })
  const jobs = (await queue.getJobs('delayed')).filter((item) => item.data.chatId == chatId).sort((a, b) => a.delay - b.delay).map((item) => item.data)
  return jobs
}

export async function querySop({
  page,
  pageSize,
  tag,
  topic
}: {
  page: number;
  pageSize: number;
  tag: string
  topic: string
}):Promise<Sop[]> {
  'use server'
  const mongoClient = AdminPrismaMongoClient.getHaoguCommonInstance()
  const sops = await mongoClient.sop.findMany({
    where:{
      tag,
      topic
    },
    take: pageSize,
    skip: pageSize * (page - 1),
    orderBy: [{ week: 'asc' }, { day:'asc' }, { time: 'asc' }],
  })
  return sops as Sop[]
}

export async function querySopById(
  id:string
):Promise<Sop> {
  'use server'
  const mongoClient = AdminPrismaMongoClient.getHaoguCommonInstance()
  const sop = await mongoClient.sop.findFirst({
    where:{
      id
    }
  })
  return sop as Sop
}

export async function querySopCount({ tag, topic }: {tag:string, topic:string}) {
  'use server'
  const mongoClient = AdminPrismaMongoClient.getHaoguCommonInstance()
  const count = await mongoClient.sop.count({ where:{ tag, topic } })
  return count
}

export async function getEnableSops() {
  const mongoClient = AdminPrismaMongoClient.getHaoguCommonInstance()
  return await mongoClient.sop.findMany({ where: { enable: true } })
}


export async function updateMq() {
  'use server'
  Config.setting.projectName = 'haogu'
  const minDay = -30
  const maxDay = 30
  //   const currentCourseWeek = DataService.getCurrentWeekCourseNo();
  // const minCourseWeek = Number(dayjs(`${currentCourseWeek}`, 'YYYYMMDD')
  //   .subtract(maxDay, 'day')
  //   .format('YYYYMMDD'))
  // const maxCourseWeek = Number(dayjs(`${currentCourseWeek}`, 'YYYYMMDD')
  //   .subtract(minDay, 'day')
  //   .format('YYYYMMDD'))
  const minCourseWeek = 0
  const maxCourseWeek = 99999

  const redisClient = RedisCacheDB.getInstance()
  const chatList = await getChatByCourseWeekRange(minCourseWeek, maxCourseWeek)
  const chatJobs = new Map<string, Map<string, Job<ITask>[]>>()
  const botIds = new Set<string>()
  for (const chat of chatList) {
    botIds.add(chat.wx_id)
  }
  for (const botId of botIds) {
    const queue = new Queue<ITask>(
      getVisualizedSopQueueName(Config.setting.projectName, botId),
      {
        connection: redisClient,
      }
    )
    const jobs = await queue.getJobs('delayed')
    const userJobsMap = new Map<string, Job[]>()
    for (const job of jobs) {
      const userId = job.data.userId
      userJobsMap.set(userId, [...(userJobsMap.get(userId) ?? []), job])
    }
    chatJobs.set(botId, userJobsMap)
  }
  for (const chat of chatList) {
    const tasks = await VisualizedSopTasks.getTaskList(
      'haogu',
      chat.contact.wx_id,
      chat.id,
      chat.wx_id
    )
    const userJobsMap = chatJobs.get(chat.wx_id) ?? new Map<string, Job<ITask>[]>()
    const jobs = userJobsMap.get(chat.contact.wx_id) ?? []
    const jobSet = new Set(jobs.map((job) => job.data.name))
    const jobMap = new Map<string, ITask>()
    for (const job of jobs) {
      jobMap.set(job.data.name, job.data)
    }
    const taskSet = new Set(tasks.map((task) => task.name))
    const taskMap = new Map<string, ITask>()
    for (const task of tasks) {
      taskMap.set(task.name, task)
    }
    for (const job of jobs) {
      const jobName: string = job.data.name
      const time = taskMap.get(jobName)?.scheduleTime
      if (!taskSet.has(jobName) || time?.week !== job.data.scheduleTime.week || time?.day !== job.data.scheduleTime.day || time?.time !== job.data.scheduleTime.time) {
        await job.remove()
      }
    }
    const willAddTasks: ITask[] = []
    for (const task of tasks) {
      const taskName: string = task.name
      const time = jobMap.get(taskName)?.scheduleTime
      if (!jobSet.has(taskName) || time?.week !== task.scheduleTime.week || time?.day !== task.scheduleTime.day || time?.time !== task.scheduleTime.time) {
        willAddTasks.push(task)
      }
    }

    // 使用Promise.all进行并发操作
    await Promise.all(
      willAddTasks.map(async (task) => {
        task.sendTime = await calTaskTime(task.scheduleTime, task.chatId)
      })
    )

    await ScheduleTask.addTasks(
      getVisualizedSopQueueName(Config.setting.projectName, chat.wx_id),
      willAddTasks
    )
  }
}

export async function validateSops(): Promise<string> {
  'use server'
  const mongoClient = AdminPrismaMongoClient.getHaoguCommonInstance()
  const sops = await mongoClient.sop.findMany()
  let error = ''
  for (const sop of sops) {
    if (!sop) continue
    error += validateSop(sop as Sop)
  }
  return error
}

function validateSop(sop: Sop) {
  let isError = false
  let content = `sop: ${sop.title}\n`
  if (sop.day < 1 || sop.day > 7) {
    isError = true
    content += `day不合法，day是${sop.day}\n`
  }
  const splitedTime = sop.time.split(':')
  //判断时间字符串是否合法
  if (
    splitedTime.length != 3 ||
    splitedTime[0].length != 2 ||
    Number(splitedTime[0]) < 0 ||
    Number(splitedTime[0]) > 24 ||
    splitedTime[1].length != 2 ||
    Number(splitedTime[1]) < 0 ||
    Number(splitedTime[1]) > 60 ||
    splitedTime[2].length != 2 ||
    Number(splitedTime[2]) < 0 ||
    Number(splitedTime[2]) > 60
  ) {
    isError = true
    content += `time不合法，time是${sop.time}\n`
  }
  for (const situation of sop.situations) {
    for (const condition of situation.conditions) {
      if (!conditionJudgeMap[condition.condition]) {
        isError = true
        content += `条件${condition.condition}不存在\n`
      }
    }
    for (const action of situation.action) {
      if (action.type == ActionType.text) {
        if (!action.description) {
          isError = true
          content += '没有填写description\n'
        }
        for (const text of action.textList) {
          if (text.type == TextType.variable) {
            if (!textVariableMap[text.tag]) {
              isError = true
              content += `字符串变量${text.tag}不存在\n`
            }
          }
        }
      } else if (action.type == ActionType.custom) {
        if (!actionCustomMap[action.tag]) {
          isError = true
          content += `自定义事件变量${action.tag}不存在\n`
        }
      } else if (action.type == ActionType.link) {
        if (
          action.source.type == LinkSourceType.variable &&
          !linkSourceVariableTagMap[action.source.tag]
        ) {
          isError = true
          content += `链接变量${action.source.tag}不存在\n`
        }
      }
    }
  }
  if (isError) {
    return `${content  }\n`
  } else {
    return ''
  }
}

export async function testAction(chatId:string, id: string) {
  'use server'
  if (!chatId) {
    throw ('chat_id不对')
  }
  const splitedChatId = chatId.split('_')
  if (splitedChatId.length != 2) {
    throw ('chat_id不对')
  }
  const botId = splitedChatId[1]
  Config.setting.projectName = 'haogu'
  Config.setting.wechatConfig = await loadConfigByWxId(botId)
  const mongoClient = AdminPrismaMongoClient.getHaoguCommonInstance()
  const sop = await mongoClient.sop.findFirst({ where: { id } })
  if (!sop) {
    throw '没有这个sop'
  }
  for (const { action } of sop.situations) {
    for (const singleAction of action) {
      await haoguVisualizedSopProcessor.handleAction(chatId, getUserId(chatId), singleAction as Action, {
        force: true,
      })
    }
  }
}

export async function testSop(chatId:string, sopId:string) {
  const userId = getUserId(chatId)
  const botId = getBotId(chatId)
  Config.setting.wechatConfig = await loadConfigByWxId(botId)
  const mongoClient = AdminPrismaMongoClient.getHaoguCommonInstance()
  const sop = await mongoClient.sop.findFirst({ where: { id:sopId } })
  if (!sop) {
    throw '没有这个sop'
  }
  await haoguVisualizedSopProcessor.handleSop(chatId, userId, sop as Sop, true)
}

export async function updateRedisSop() {
  'use server'
  const redisClient = RedisDB.getInstance()
  const sops = await getEnableSops()
  const accounts = await queryAccounts()
  const tags = (await queryAllTags()).filter((tag) => tag.enable)
  const originTopics = await queryAllSopTopics()
  const topics = originTopics.filter((topic) => topic.enable)

  const keys = await redisClient.keys('haogu:sop_topic:*')
  for (const key of keys) {
    await redisClient.del(key)
  }
  for (const topic of originTopics) {
    await redisClient.set(getSopTopicConditionRedisKey('haogu', topic.tag, topic.name), JSON.stringify(topic.conditions))
  }

  for (const account of accounts) {
    const tagsOfAccount = tags.filter((tag) => tag.enable_account.includes(account.id))
    const topicsOfAccount = topics.filter((topic) => tagsOfAccount.map((tag) => tag.name).includes(topic.tag))
    const finalSops = sops.filter((sop) => tagsOfAccount.map((tag) => tag.name).includes(sop.tag) && topicsOfAccount.map((topic) => topic.name).includes(sop.topic))
    await redisClient.set(getVisualizedRedisSopKey('haogu', account.wechatId), JSON.stringify(finalSops))
  }
}

export async function testAllSop(tag:string, chatId:string, topic:string) {
  const botId = chatId.split('_')[1]
  const userId = getUserId(chatId)
  Config.setting.wechatConfig = await loadConfigByWxId(botId)
  const mongoClient = AdminPrismaMongoClient.getHaoguCommonInstance()
  const sops = await mongoClient.sop.findMany({ where:{ enable:true, tag, topic }, orderBy:[{ week:'asc' }, { day:'asc' }, { time:'asc' }] })
  for (const sop of sops) {
    await commonMessageSender.sendText(chatId, {
      text: `发送sop${sop.title} ${sop.week}周 ${sop.day}天 ${sop.time}`,
      description: 'sop 描述'
    }, {
      force:true
    })
    await sleep(800)
    for (const { action } of sop.situations) {
      for (const singleAction of action) {
        await haoguVisualizedSopProcessor.handleAction(chatId, userId, singleAction as Action, {
          force: true,
        })
        await sleep(800)
      }
    }
  }
}

export async function deleteSop(id:string) {
  const mongoClient = AdminPrismaMongoClient.getHaoguCommonInstance()
  await mongoClient.sop.delete({ where:{ id } })
}

export async function SaveSop(sop:Omit<Sop, 'id'>) {
  'use server'
  const mongoClienet = AdminPrismaMongoClient.getHaoguCommonInstance()
  await mongoClienet.sop.create({ data:sop })
}

export async function getConditionJudgeKeys() {
  'use server'
  return Object.keys(conditionJudgeMap)
}

export async function getCustomKeys() {
  'use server'
  return Object.keys(actionCustomMap)
}

export async function getVariableMapKeys() {
  'use server'
  return Object.keys(textVariableMap)
}

export async function getLinkSourceVariableTagKeys() {
  'use server'
  return Object.keys(linkSourceVariableTagMap)
}

export async function updateSop(sop_id:string, sop:Partial<Sop>) {
  'use server'
  const mongoClient = AdminPrismaMongoClient.getHaoguCommonInstance()
  await mongoClient.sop.update({ where:{ id:sop_id }, data:sop })
}

export async function copySop(sop_id:string):Promise<Sop> {
  const mongoClient = AdminPrismaMongoClient.getHaoguCommonInstance()
  const sop = await mongoClient.sop.findFirst({ where:{ id:sop_id } })
  if (!sop) throw ('没有这个sop')
  const newSop = await mongoClient.sop.create({ data:{
    ...sop,
    id:undefined,
    title: `${sop.title}_copy`,
    situations:sop.situations as Situation[]
  } })
  return newSop as Sop
}

export async function resetSop(chatId:string): Promise<void> {
  await VisualizedSopTasks.clearSop('haogu', chatId, getBotId(chatId))
  await startTasks(chatStateStoreClient, 'haogu', getUserId(chatId), chatId, calTaskTime, true)
}

export async function queryAllSop():Promise<Sop[]> {
  const mongoClient = AdminPrismaMongoClient.getHaoguCommonInstance()
  const result = await mongoClient.sop.findMany()
  return result as Sop[]
}