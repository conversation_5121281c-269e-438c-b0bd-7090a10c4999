import { Node } from 'moer_overseas/workflow/nodes/types'
import { queryChatById, changeCourseNo, changeNextStage, changePhone, setChatLanguage } from '@/app/moer_overseas/api/chat'
import { notImplement } from '@/lib/not_implement'
import UserEdit from '@/app/moer_overseas/component/edit'

export default async function Page({ params }: { params: Promise<{ id: string }> }) {
  const param = await params
  return <UserEdit
    id={decodeURIComponent(param.id)}
    queryChatById={queryChatById}
    changeCourseNo={changeCourseNo}
    changeNextStage={changeNextStage}
    changePhone={changePhone}
    stageOption={Object.values(Node)}
    resetSop={notImplement}
    clearCache={notImplement }
    updateIsPaid={notImplement}
    setChatLanguage={setChatLanguage}
    updatePayTime={notImplement}
  />
}