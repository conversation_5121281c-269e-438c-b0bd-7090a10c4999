generator client {
  provider = "prisma-client-js"
  output   = "../prisma_client"
}

datasource db {
  provider = "mongodb"
  url      = "mongodb://root:free1234$spirit!@dds-bp100ccf7c2e7f741791-pub.mongodb.rds.aliyuncs.com:3717/haogu?authSource=admin"
}

type ChatContact {
  wx_id   String
  wx_name String
}

type ChatState {
  nodeInvokeCount Json
  nextStage       String
  userSlots       Json
  state           Json
}

model chat {
  id                       String      @id @map("_id")
  contact                  ChatContact
  round_ids                String[]
  wx_id                    String
  is_human_involved        <PERSON>ole<PERSON>?
  created_at               DateTime?   @db.Date
  chat_state               ChatState
  course_no                Int?
  pay_time                 DateTime?
  is_deleted               Boolean?
  is_stop_group_push       Boolean? // 停止群发
  phone                    String? // 手机号
  conversation_id          String
  customer_unified_user_id String

  @@index([phone])
  @@index([wx_id, course_no])
  @@index([course_no])
  @@index([contact.wx_name])
  @@index([created_at])
  @@index([conversation_id])
  @@index([customer_unified_user_id])
}

model chat_history {
  id         String   @id @default(auto()) @map("_id") @db.ObjectId
  chat_id    String
  content    String
  created_at DateTime @db.Date
  role       String

  is_send_by_human  Boolean? // 是否人工回复
  short_description String? // SOP 描述
  round_id          String? // LLM 输出，会绑定 round_id
  is_recalled       Boolean? // 撤回
  message_id        String?    @unique // 消息 ID
  chat_state        ChatState? // 产生回复时的 chat_state
  sop_id            String?
  state             String?

  @@index([chat_id, created_at], map: "chat_id_1_created_at_1")
  @@index([role])
}
