import { ChatHistoryService } from 'service/chat_history/chat_history'
import { ChatDB } from 'service/database/chat'
import { ChatStateStore } from 'service/local_cache/chat_state_store'
import { PrismaMongoClient } from '../../database/prisma'

export const chatDBClient = new ChatDB(PrismaMongoClient.getCommonInstance())
export const chatStateStoreClient = new ChatStateStore(chatDBClient)
export const chatHistoryServiceClient = new ChatHistoryService(PrismaMongoClient.getCommonInstance(), chatStateStoreClient)