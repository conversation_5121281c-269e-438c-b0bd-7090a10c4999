import { SendMessageResultHandler } from 'service/message_handler/juzi/send_result_handler'
import { WecomCommonMessageSender } from 'service/visualized_sop/common_sender/wecom'
import { chatDBClient, chatHistoryServiceClient } from './base_instance'
import { WecomMessageSender } from 'service/message_handler/juzi/message_sender'
import { FreeThink } from 'service/agent/freethink'
import { Reply } from 'service/agent/reply'
import { EventTracker } from 'model/logger/data_driven'
import { PrismaMongoClient } from '../../database/prisma'

export const sendMessageResultHandlerClient = new SendMessageResultHandler(chatHistoryServiceClient)
export const wecomMessageSender = new WecomMessageSender(chatHistoryServiceClient)
export const commonMessageSender = new WecomCommonMessageSender(wecomMessageSender, chatHistoryServiceClient)

export const replyClient = new Reply(chatDBClient, chatHistoryServiceClient, commonMessageSender)

export const eventTrackClient = new EventTracker(PrismaMongoClient.getCommonInstance())
export const freeThinkClient = new FreeThink(chatHistoryServiceClient, eventTrackClient)