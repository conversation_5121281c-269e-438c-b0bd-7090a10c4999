import { SilentReAsk } from 'service/schedule/silent_requestion'
import logger from 'model/logger/logger'

export enum TaskName {
  test_task = 'test_task',
}


/**
 * 延迟任务
 */
export class TaskRegister {
  public static register() {
    // 注册测试任务
    SilentReAsk.registerTask(TaskName.test_task, async (chat_id: string, params) => {
      logger.log(`执行测试任务 for chat: ${chat_id}`, params)
    })
  }
}


