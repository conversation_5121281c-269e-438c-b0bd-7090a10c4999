import { getState } from 'service/llm/state'
import { ContextBuilder } from '../context'
import { Config } from 'config/config'

describe('Test', function () {
  beforeAll(() => {
    Config.setting.projectName = 'yuhe'
  })

  it('ContextBuilder', async () => {
    await ContextBuilder.build({
      state: await getState('7881302298050442_1688856156687987', '7881302298050442'),
      retrievedKnowledge: true,
      customerMemory: true,
      customerBehavior: true,
      talkStrategyPrompt: '正常回复？？',
      injectChatHistory: true,
      debug: true,
    })
  }, 60000)

  it('getCustomerPortrait', async() => {
    const state = await getState('', '')
    const chatId = '7881302298050442_1688857404698934'
    const userSlots = await new ContextBuilder({ state }).customerPortrait(chatId)
    console.log(userSlots)
  }, 600000)

  it('getCustomerBehavior', async () => {
    const state = await getState('', '')
    const chatId = '7881301446232150_1688858335726355'
    console.log(await new ContextBuilder({ state }).customerBehavior(chatId))
  }, 60000)
})