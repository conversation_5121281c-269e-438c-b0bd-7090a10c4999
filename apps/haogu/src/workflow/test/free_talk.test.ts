import { getState, IWorkflowState } from 'service/llm/state'
import { FreeTalk } from 'service/agent/freetalk'
import { ChatInterruptHandler } from 'service/message_handler/interrupt/interrupt_handler'
import { faker } from '@faker-js/faker'
import { ContextBuilder } from '../context'
import { TaskRegister } from '../schedule_task/register_task'
import { chatHistoryServiceClient } from '../../config/instance/base_instance'
import { eventTrackClient, replyClient } from '../../config/instance/send_message_instance'
import { stageFilter } from '../meta_action/stage'

describe('Test', function () {
  beforeAll(() => {

  })

  it('should pass', async () => {
  }, 60000)


  it('freeTalkTest', async () => {
    const chatId = '7881300516060552_1688857404698934'
    await mockUserData(chatId)
    const userMessage = '泥嚎'
    await chatHistoryServiceClient.addUserMessage(chatId, userMessage)

    const state: IWorkflowState = {
      chat_id: chatId,
      user_id: faker.string.uuid(),
      round_id: faker.string.uuid(),
      userMessage: userMessage,
      interruptHandler: await ChatInterruptHandler.create(chatId),
    }
    TaskRegister.register()
    const freeTalk = new FreeTalk(chatHistoryServiceClient, ContextBuilder, eventTrackClient, replyClient, stageFilter)
    // DataService.getCurrentTime = async () => {
    //   return {
    //     day: 1,
    //     time: '16:00:00',
    //   }
    // }
    // DataService.isPaidSystemCourse = async () => {
    //   return false
    // }

    await freeTalk.invoke(state)
  }, 9E8)
})

export async function mockUserData(chatId: string) {
  await mockChatHistory(chatId)
  await mockUserSlot(chatId)
}

async function mockUserSlot(chatId: string) {
  const state = await getState('', '')
  const userSlot = await new ContextBuilder({ state }).customerPortrait(chatId)
  new ContextBuilder({ state }).customerPortrait = async (chatId: string) => {
    return userSlot
  }
}

async function mockChatHistory(chatId: string) {
  const fullChatHistory = await chatHistoryServiceClient.getChatHistoryByChatId(chatId)
  chatHistoryServiceClient.getChatHistoryByChatId = async (chat_id: string) => {
    return fullChatHistory
  }
}