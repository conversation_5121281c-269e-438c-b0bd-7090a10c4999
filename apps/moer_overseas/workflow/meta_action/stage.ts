import { chatStateStoreClient } from '../../service/base_instance'
import { DataService } from '../../helper/getter/get_data'
import { GenericStage, registerMetaActions, registerThinkPrompt, StageFilter, StageSpec } from 'service/agent/stage'
import { isScheduleTimeAfter } from '../../helper/tool/creat_schedule_task'
import { MetaActions, ThinkPrompt } from './context'
import { PostAction } from './post_action'

// ---- Stage registry (declarative) ----
registerMetaActions(MetaActions)
registerThinkPrompt(ThinkPrompt)
const STAGES: StageSpec[] = [
  {
    id: 'afterPaid',
    isActive: async (chatId) => await DataService.isPaidSystemCourse(chatId),
  },
  {
    id: 'duringCourse',
    isActive: async (chatId) => {
      const currentTime = await DataService.getCurrentTime(chatId)
      return await DataService.isWithinClassTime(currentTime)
    },
  },
  {
    id: 'afterCourse4',
    isActive: async (chatId) => {
      const currentTime = await DataService.getCurrentTime(chatId)
      return isScheduleTimeAfter(currentTime, { is_course_week: true, day: 4, time: '22:20:00' })
    },
    actions: {
      '发起报名邀约': PostAction.sendInvitation,
      '发送学员案例': PostAction.sendCaseImage,
    },
  },
  {
    id: 'afterCourse3',
    isActive: async (chatId) => {
      const currentTime = await DataService.getCurrentTime(chatId)
      return isScheduleTimeAfter(currentTime, { is_course_week: true, day: 3, time: '21:00:00' })
    },
    actions: {
      '发起报名邀约': PostAction.sendInvitation,
      '发送学员案例': PostAction.sendCaseImage,
    },
  },
  {
    id: 'afterCourse1',
    isActive: async (chatId) => {
      const currentTime = await DataService.getCurrentTime(chatId)
      return isScheduleTimeAfter(currentTime, { is_course_week: true, day: 1, time: '20:00:00' })
    },
  },
  {
    id: 'afterBonding',
    isActive: async (chatId) => (await chatStateStoreClient.getNodeCount(chatId, 'UserMessage')) > 15,
    actions: {
      '提醒完成小讲堂': PostAction.sendPreCourseLink,
    },
  },
  {
    id: 'afterAdding',
    isActive: async (chatId) => (await chatStateStoreClient.getNodeCount(chatId, 'UserMessage')) <= 15,
    actions: {
      '提醒完成小讲堂': PostAction.sendPreCourseLink,
    },
  },
]

// ---- Router wiring (order matters: strictly reverse chronological flow) ----
export const stageFilter = new StageFilter(
  STAGES.map((spec) => new GenericStage(spec))
) // 必须严格按照流程倒序添加