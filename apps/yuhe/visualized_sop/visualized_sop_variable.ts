import { UserSlots } from 'service/user_slots/extract_user_slots'
import { DataService } from '../helper/getter/get_data'
import { IChattingFlag } from '../state/user_flags'
import { ExtractUserSlots } from '../user_slots/user_slots_extraction'
import { PostAction } from '../workflow/meta_action/post_action'
import { getState } from 'service/llm/state'
import { LLM } from 'lib/ai/llm/llm_model'
import { HandleActionOption } from 'service/visualized_sop/visualized_sop_processor'
import { LLMReply } from 'service/llm/llm_reply'
import { JuziAPI } from 'model/juzi/api'
import { ContextBuilder } from '../workflow/context'
import { AddUserToGroup } from '../helper/group/add_user_to_group'
import { PrismaMongoClient } from '../database/prisma'
import { sendMsg } from '../helper/send_msg'
import { getBotId } from 'config/chat_id'
import { YuHeConfig } from '../config/config'
import { YuHeAccountType } from 'config'
import dayjs from 'dayjs'
import { chatDBClient, chatHistoryServiceClient, chatStateStoreClient } from '../service/base_instance'
import { Node } from '../workflow/nodes/node'

export const conditionJudgeMap:Record<string, ((params:{chatId:string;userId:string})=>Promise<boolean>)> = {
  '基础信息已经填写': async({ chatId }) => {
    await new ExtractUserSlots().extractUserSlots(chatId, 6, { chat_id:chatId })
    const state = await chatStateStoreClient.get(chatId)
    const userSlotsRecord = state.userSlots
    if (!userSlotsRecord) return false
    const userSlots = UserSlots.fromRecord(userSlotsRecord)
    return isFillUserSlots(userSlots)
  },
  '基础信息只填写了一点点':async({ chatId }) => {
    const state = await chatStateStoreClient.get(chatId)
    const userSlotsRecord = state.userSlots
    if (!userSlotsRecord) return false
    const userSlots = UserSlots.fromRecord(userSlotsRecord)
    return isFillAnyUserSlots(userSlots)
  },
  '已经延期': async({ chatId }) => {
    const mongoClient = PrismaMongoClient.getInstance()
    const userInfo = await mongoClient.chat.findFirst({ where:{ id:chatId } })
    return Boolean(userInfo?.course_no_ori)
  },
  '餐饮行业学员':async({ chatId }) => {
    const industry = await UserSlots.getUserSlotSubTopicContent(chatStateStoreClient, chatId, '基本信息', '行业类目')
    return industry.includes('餐饮')
  },
  '美业行业学员':async({ chatId }) => {
    const industry = await UserSlots.getUserSlotSubTopicContent(chatStateStoreClient, chatId, '基本信息', '行业类目')
    return industry.includes('美业')
  },
  '建材行业学员':async({ chatId }) => {
    const industry = await UserSlots.getUserSlotSubTopicContent(chatStateStoreClient, chatId, '基本信息', '行业类目')
    return industry.includes('装修建材')
  },
  '其他、服装行业学员': async({ chatId }) => {
    const industry = await UserSlots.getUserSlotSubTopicContent(chatStateStoreClient, chatId, '基本信息', '行业类目')
    return !industry.includes('餐饮') && !industry.includes('美业') && !industry.includes('装修建材')
  },
  '已经报名学员': async({ chatId }) => {
    return await DataService.isPaidSystemCourse(chatId)
  },
  '完成第一节课作业': async({ chatId }) => {
    const flags = await chatStateStoreClient.getFlags<IChattingFlag>(chatId)
    if (flags.is_complete_homework1) {
      return true
    } else {
      return false
    }
  },
  '完成第二节课作业': async({ chatId }) => {
    const flags = await chatStateStoreClient.getFlags<IChattingFlag>(chatId)
    if (flags.is_complete_homework2) {
      return true
    } else {
      return false
    }
  },
  '完成第三节课作业': async({ chatId }) => {
    const flags = await chatStateStoreClient.getFlags<IChattingFlag>(chatId)
    if (flags.is_complete_homework3) {
      return true
    } else {
      return false
    }
  },
  '目前在线': async({ chatId }) => {
    const flags = await chatStateStoreClient.getFlags<IChattingFlag>(chatId)
    return flags.is_in_live_room ?? false
  },
  '第一节课完课': async({ chatId }) => {
    return await DataService.isCompletedCourse(chatId, 1)
  },
  '第二节课完课': async({ chatId }) => {
    return await DataService.isCompletedCourse(chatId, 2)
  },
  '第三节课完课': async({ chatId }) => {
    return await DataService.isCompletedCourse(chatId, 3)
  },
  '第四节课完课': async({ chatId }) => {
    return await DataService.isCompletedCourse(chatId, 4)
  },
  '第一节课到课': async({ chatId }) => {
    return await DataService.isAttendCourse(chatId, 1)
  },
  '第二节课到课': async({ chatId }) => {
    return await DataService.isAttendCourse(chatId, 2)
  },
  '第三节课到课': async({ chatId }) => {
    return await DataService.isAttendCourse(chatId, 3)
  },
  '第四节课到课': async({ chatId }) => {
    return await DataService.isAttendCourse(chatId, 4)
  },
  '第一节课要回放': async({ chatId }) => {
    const state = await chatStateStoreClient.getFlags<IChattingFlag>(chatId)
    if (state.is_ask_day1_replay) {
      return true
    } else {
      return false
    }
  },
  '第二节课要回放': async({ chatId }) => {
    const state = await chatStateStoreClient.getFlags<IChattingFlag>(chatId)
    if (state.is_ask_day2_replay) {
      return true
    } else {
      return false
    }
  },
  '第三节课要回放': async({ chatId }) => {
    const state = await chatStateStoreClient.getFlags<IChattingFlag>(chatId)
    if (state.is_ask_day3_replay) {
      return true
    } else {
      return false
    }
  },
  '第四节课要回放': async({ chatId }) => {
    const state = await chatStateStoreClient.getFlags<IChattingFlag>(chatId)
    if (state.is_ask_day4_replay) {
      return true
    } else {
      return false
    }
  },
  '第一节课看课30分钟': async({ chatId }) => {
    return await DataService.isAttendCourseMoreThanCertainDuration(chatId, 1, 30)
  },
  '第二节课看课30分钟': async({ chatId }) => {
    return await DataService.isAttendCourseMoreThanCertainDuration(chatId, 2, 30)
  },
  '第三节课看课30分钟': async({ chatId }) => {
    return await DataService.isAttendCourseMoreThanCertainDuration(chatId, 3, 30)
  },
  '第四节课看课30分钟': async({ chatId }) => {
    return await DataService.isAttendCourseMoreThanCertainDuration(chatId, 4, 30)
  },
  '已经发送第一节课回放': async({ chatId }) => {
    const state = await chatStateStoreClient.getFlags<IChattingFlag>(chatId)
    if (state.is_send_course_replay_day1) {
      return true
    } else {
      return false
    }
  },
  '已经发送第二节课回放': async({ chatId }) => {
    const state = await chatStateStoreClient.getFlags<IChattingFlag>(chatId)
    if (state.is_send_course_replay_day2) {
      return true
    } else {
      return false
    }
  },
  '已经发送第三节课回放': async({ chatId }) => {
    const state = await chatStateStoreClient.getFlags<IChattingFlag>(chatId)
    if (state.is_send_course_replay_day3) {
      return true
    } else {
      return false
    }
  },
  '已经发送第四节课回放': async({ chatId }) => {
    const state = await chatStateStoreClient.getFlags<IChattingFlag>(chatId)
    if (state.is_send_course_replay_day4) {
      return true
    } else {
      return false
    }
  },
  '已经发送第一节课完课礼': async({ chatId }) => {
    const state = await chatStateStoreClient.getFlags<IChattingFlag>(chatId)
    if (state.is_send_course1_gift) {
      return true
    } else {
      return false
    }
  },
  '已经发送第二节课完课礼': async({ chatId }) => {
    const state = await chatStateStoreClient.getFlags<IChattingFlag>(chatId)
    if (state.is_send_course2_gift) {
      return true
    } else {
      return false
    }
  },
  '已经发送第三节课完课礼': async({ chatId }) => {
    const state = await chatStateStoreClient.getFlags<IChattingFlag>(chatId)
    if (state.is_send_course3_gift) {
      return true
    } else {
      return false
    }
  },
  '已经发送第四节课完课礼': async({ chatId }) => {
    const state = await chatStateStoreClient.getFlags<IChattingFlag>(chatId)
    if (state.is_send_course4_gift) {
      return true
    } else {
      return false
    }
  },
  '24小时未回复': async({ chatId }) => {
    const isLastMsgWithin24Hours = await chatHistoryServiceClient.isLastMessageWithDuration(chatId, 60 * 24, 'minute')
    return !isLastMsgWithin24Hours
  },
  '已经完成抖音截图诊断': async({ chatId }) => {
    const flags = await chatStateStoreClient.getFlags<IChattingFlag>(chatId)
    return flags.is_complete_douyin_analysis ?? false
  },
  '有抖音号': async({ chatId }) => {
    return await DataService.isDoingDouyin(chatId)
  },
  '有手机号': async({ chatId }) => {
    const chat = await chatDBClient.getById(chatId)
    if (chat?.phone) {
      return true
    } else {
      return false
    }
  },
  '准成交': async({ chatId }) => {
    const flags = await chatStateStoreClient.getFlags<IChattingFlag>(chatId)
    return flags.is_before_payment ?? false
  },
  // 优化后的意向度相关条件 - 使用 user_flags
  '高意向客户': async({ chatId }) => {
    const flags = await chatStateStoreClient.getFlags<IChattingFlag>(chatId)
    return flags.intent_level_high ?? false
  },
  '中意向客户': async({ chatId }) => {
    const flags = await chatStateStoreClient.getFlags<IChattingFlag>(chatId)
    return flags.intent_level_medium ?? false
  },
  '低意向客户': async({ chatId }) => {
    const flags = await chatStateStoreClient.getFlags<IChattingFlag>(chatId)
    return flags.intent_level_low ?? false
  },
  '零意向客户': async({ chatId }) => {
    const flags = await chatStateStoreClient.getFlags<IChattingFlag>(chatId)
    return flags.intent_level_zero ?? false
  },
  '未进群': async({ chatId }) => {
    const mongoClient = PrismaMongoClient.getInstance()
    const userInfo = await mongoClient.chat.findFirst({ where:{ id:chatId } })
    if (!userInfo || !userInfo.course_no) {
      return false
    }
    const groupInfo = await mongoClient.group.findFirst({ where:{ course_no:userInfo.course_no } })
    if (!groupInfo) {
      return false
    }
    return !await JuziAPI.isInGroup({
      imBotId: userInfo.wx_id,
      imRoomId: groupInfo.group_id,
      imContactId: userInfo.contact.wx_id
    })
  },
  '中神通老师': async({ chatId }) => {
    const botId = getBotId(chatId)
    return YuHeConfig.getYuHeAccountType(botId) == YuHeAccountType.ZhongShenTong
  },
  '志诚老师': async({ chatId }) => {
    const botId = getBotId(chatId)
    return YuHeConfig.getYuHeAccountType(botId) == YuHeAccountType.ZhiCheng
  },
  '早上创建的客户': async({ chatId }) => {
    const mongoClient = PrismaMongoClient.getInstance()
    const userInfo = await mongoClient.chat.findFirst({ where:{ id:chatId } })
    if (!userInfo) {
      return false
    }
    if (dayjs(userInfo.created_at).hour() < 12) {
      return true
    } else {
      return false
    }
  }
}

export const textVariableMap:Record<string, (params:{chatId:string;userId:string})=> Promise<string>> = {
  '客户昵称': async({ chatId }) => {
    return await DataService.getUserName(chatId)
  },
  '第一节课链接': async({ chatId }) => {
    return await DataService.getCourseLink(chatId, 1) ?? ''
  },
  '第二节课链接': async({ chatId }) => {
    return await DataService.getCourseLink(chatId, 2) ?? ''
  },
  '第三节课链接': async({ chatId }) => {
    return await DataService.getCourseLink(chatId, 3) ?? ''
  },
  '第四节课链接': async({ chatId }) => {
    return await DataService.getCourseLink(chatId, 4) ?? ''
  },
  '第一节课回放链接': async({ chatId }) => {
    // return await DataService.getCourseLink(chatId, 1) ?? ''
    return 'https://z2f8xek59y.fki3.oucth.com/ccc6d2eabd712219ec5e51653730636024c58399e8ea1cbb609cfb3bdfb8fbd0f5d27591d1a77aaabf91f91bddf4983190f6b08461580a73'
  },
  '第二节课回放链接': async({ chatId }) => {
    // return await DataService.getCourseLink(chatId, 2) ?? ''
    return 'https://z2f8xek59y.fki3.oucth.com/ccc6d2eabd712219ec5e51653730636024c58399e8ea1cbb609cfb3bdfb8fbd0b472bdf3d3194663b75d0931e2e7bbc290f6b08461580a73'
  },
  '第三节课回放链接': async({ chatId }) => {
    // return await DataService.getCourseLink(chatId, 3) ?? ''
    return 'https://z2f8xek59y.fki3.oucth.com/ccc6d2eabd712219ec5e51653730636024c58399e8ea1cbb609cfb3bdfb8fbd0f5d27591d1a77aaab9bbb674e7358c9290f6b08461580a73'
  },
  '第四节课回放链接': async({ chatId }) => {
    // return await DataService.getCourseLink(chatId, 4) ?? ''
    return 'https://z2f8xek59y.fki3.oucth.com/ccc6d2eabd712219ec5e51653730636024c58399e8ea1cbb609cfb3bdfb8fbd0a444dfa415ad369b3fd1559ac014019390f6b08461580a73'
  },
  // 优化后的意向度相关文本变量 - 使用 user_flags
  '客户意向度等级': async({ chatId }) => {
    const flags = await chatStateStoreClient.getFlags<IChattingFlag>(chatId)

    if (flags.intent_level_high) return '高意向'
    if (flags.intent_level_medium) return '中意向'
    if (flags.intent_level_low) return '低意向'
    if (flags.intent_level_zero) return '零意向'

    return '未知'
  },
}

export const actionCustomMap:Record<string, (params:{chatId:string;userId:string;opt:HandleActionOption})=> Promise<void>> = {
  '重置挖需': async({ chatId }) => {
    const chatInfo = await chatDBClient.getById(chatId)
    if (!chatInfo?.phone) {
      return
    }
    const state = await chatStateStoreClient.getFlags<IChattingFlag>(chatId)
    state.is_already_ask_douyin_screenshot = false
    state.is_send_first_ask_intention = true
    await chatStateStoreClient.update(chatId, {
      state:state,
      nextStage: Node.IntentionQuery
    })
  },
  '发案例': async({ chatId, userId, opt }) => {
    const action = await PostAction.sendCaseImage(chatId)
    if (action.callback) {
      await action.callback()
      console.log(action.guidance)
      const state = await getState(chatId, userId)
      const context = await ContextBuilder.build({
        state,
        customerChatRounds: 0,
        customPrompt: '# 动态话术\n你需要根据任务与客户画像来生成简短、自然、贴近客户的销售话术，直接面向客户，如果没有称呼，就叫老板',
        talkStrategyPrompt: `你刚给客户发送了同行学员成功案例，请在此基础上跟客户简单介绍下成功案例，并且选择匹配客户的销售策略发起主动销售
${action.guidance}

# 策略话术示例
- 软性引导销售策略（引起兴趣、逐步引导购买）
话术示例：老板，我注意到您之前尝试过抖音运营，但效果可能不如预期。我们最近有个学员做了类似的美业项目，通过AI工具和短视频的结合，短时间内成功吸引了大量精准客户。您是否也有兴趣了解这个方法呢？
- 解决痛点销售策略（针对客户的具体问题，提供解决方案）
话术示例：我了解到您现在面临的主要问题是如何吸引精准的顾客和提高转化率。其实，很多像您这样的美容店，都在尝试通过短视频和AI工具来解决这些痛点。如果您想，我可以详细介绍一下我们课程里如何帮助学员解决这些问题
- 价值展示销售策略（强调课程带来的价值和长期收益）
话术示例：老板，参加《AI盈利陪跑营》不仅可以学到短视频创作技巧，还能帮助您搭建自己的流量矩阵，提升品牌曝光率。通过这种系统化的训练，您可以实现2万/月的收入目标，而课程的投入是值得的，您考虑一下这样长远的价值`,
      })

      const llm = new LLM({
        temperature: 0.8,
        maxTokens: 400,
        projectName:'yuhe',
        promptName: 'sop_send_salecase',
        model: 'gpt-5-mini',
        meta: {
          chat_id: state.chat_id,
          round_id: state.round_id,
          promptName: 'sop_send_salecase',
        },
      })
      const res = await llm.predictMessage(context)
      const splitSentence = LLMReply.splitIntoSentencesWithMaxSentences(res, 2)
      await sendMsg(userId, chatId, splitSentence, '发案例', false, state.round_id, opt.sop_id)
    }
  },
  '进入复训阶段': async ({ chatId }) => {
    await PostAction.enterPostpone(chatId)
  },
  '拉群': async({ chatId }) => {
    await AddUserToGroup.addUserToGroup(chatId)
  }
}


export const linkSourceVariableTagMap:Record<string, (params:{chatId:string;userId:string})=>Promise<string>> = {
  'hello world': async () => {
    return 'hello world'
  }
}

export function isFillAnyUserSlots(userSlots:UserSlots):boolean {
  if (userSlots.isTopicSubTopicExist('抖音运营状态', '是否抖音在做')) return true
  if (userSlots.isTopicSubTopicExist('基本信息', '行业类目')) return true
  if (userSlots.isTopicSubTopicExist('基本信息', '年营业额')) return true
  if (userSlots.isTopicSubTopicExist('基本信息', '所在城市')) return true
  if (userSlots.isTopicExist('想要解决的问题')) return true
  return false
}


export function isFillUserSlots(userSlots:UserSlots): boolean {
  if (!userSlots.isTopicSubTopicExist('抖音运营状态', '是否抖音在做')) return false
  if (!userSlots.isTopicSubTopicExist('基本信息', '行业类目')) return false
  if (!userSlots.isTopicExist('想要解决的问题')) return false
  return true
}