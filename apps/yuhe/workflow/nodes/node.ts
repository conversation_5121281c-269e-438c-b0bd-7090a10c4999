import logger from 'model/logger/logger'
import { chatStateStoreClient } from '../../service/base_instance'
import { eventTrackClient } from '../../service/event_track_instance'
import { IEventType } from 'model/logger/data_driven'
import { IWorkflowState } from 'service/llm/state'

export enum Node {
  Dummy = 'dummy', // 占位假节点，在路由的时候 return 这个节点，表示在路由期间直接执行逻辑，不再进行后续路由判断。会继续执行上次的节点
  DummyEnd = 'dummy_end', // 占位假节点，在路由的时候 return 这个节点，表示在路由期间已经执行完所有逻辑，不执行后续节点的逻辑
  FreeTalk = 'free_talk', // 闲聊节点
  PhoneQuery = 'phone_query',
  SendFile = 'send_file', // 发送文件节点
  IntentionQuery = 'intention_query', // 挖需节点
  DouyinAnalysis = 'douyin_analysis', // 抖音分析节点
  Homework1 = 'homework1',
  Homework2 = 'homework2',
}

export abstract class WorkFlowNode {
  public static async invoke(state: IWorkflowState): Promise<Node> {
    return Node.FreeTalk
  }
}

export function trackInvoke (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
  const originalMethod = descriptor.value

  descriptor.value = async function (...args: any []) {
    const state: IWorkflowState = args [0] // 假设 state 对象存在于第一个参数
    const chatId = state.chat_id // 假设 state 中包含 chatId
    const roundId = state.round_id // 假设 state 中包含 roundId


    const nodeInvokeCount: Record<string, number> = (await chatStateStoreClient.get(state.chat_id)).nodeInvokeCount
    const node_name = target.name

    if (!nodeInvokeCount [node_name]) {
      nodeInvokeCount [node_name] = 0
    }

    const stateInfo = {
      chatId,
      roundId,
      userMessage: state.userMessage
    }

    // 输出进入节点的信息
    logger.debug({ chat_id: chatId, round_id: roundId },
      `进入 ${node_name}`,
      `调用次数: ${nodeInvokeCount[node_name]}`,
      stateInfo
    )

    eventTrackClient.track(chatId, IEventType.NodeInvoke, {
      chat_id: chatId,
      round_id: roundId,
      node_name: node_name,
      node_count: nodeInvokeCount[node_name]
    })

    const start = Date.now()
    let res
    try {
      res = await originalMethod.apply (this, args)
    } catch (e) {
      logger.error({ chat_id: chatId, round_id: roundId }, `调用 ${node_name} 出错`, e, `round_id: ${roundId}`) // 输出)
      throw e
    }

    const end = Date.now()
    logger.debug({ chat_id: chatId, round_id: roundId },
      `结束 ${target.name}`,
      `执行时长: ${((end - start) / 1000).toFixed(1)}s`,
      stateInfo
    ) // 输出结束节点的信息

    nodeInvokeCount [node_name]++ // 消息有可能被打断，计数放到后面

    return res
  }

  return descriptor
}
