import logger from 'model/logger/logger'
import { AsyncLock } from 'model/lock/lock'
import { getState, IWorkflowState } from 'service/llm/state'
import { FreeTalkNode, Router } from './router'
import { sendYuHeWelComeMessage } from '../client/event_handler'
import { BaseWorkFlow } from 'service/workflow/workflow'
import { InterruptError } from 'service/message_handler/interrupt/interrupt_handler'
import { HumanTransfer, HumanTransferType } from '../human_transfer/human_transfer'
import { Homework1Node, Homework1Store } from './nodes/homework1'
import { Homework2Node, Homework2Store, HomeworkDurationUserMessageStore } from './nodes/homework2'
import { sleep } from 'openai/core'
import { VisualizedSopTasks } from 'service/visualized_sop/visualized_sop_task_starter'
import { Config } from 'config'
import { memoryStoreClient, } from '../service/instance'
import { sendMsg } from '../helper/send_msg'
import { ExtractUserSlots } from '../user_slots/user_slots_extraction'
import { GroupNotification } from 'service/group_notification/group_notification'
import { chatDBClient, chatHistoryServiceClient, chatStateStoreClient } from '../service/base_instance'
import { wecomMessageSender } from '../service/send_message_instance'
import { Node, WorkFlowNode } from './nodes/node'
import { SendFileNode } from './nodes/sendfile'
import { IntentionQueryNode } from './nodes/intention_query'
import { DouyinAnalysisNode } from './nodes/douyin_analysis'
import { PhoneQueryNode } from './nodes/phone_query'

export const NodeMap: { [key in Node]?: typeof WorkFlowNode } = {
  [Node.FreeTalk]: FreeTalkNode,
  [Node.SendFile]: SendFileNode,
  [Node.IntentionQuery]: IntentionQueryNode,
  [Node.DouyinAnalysis]: DouyinAnalysisNode,
  [Node.Homework1]: Homework1Node,
  [Node.Homework2]: Homework2Node,
  [Node.PhoneQuery]: PhoneQueryNode
}

export class Workflow extends BaseWorkFlow {
  /**
     * 对话流程
     * @param chat_id
     * @param user_id
     * @param userMessage
     */
  public static async step(chat_id: string, user_id: string, userMessage: string) {
    try {
      userMessage = this.transferWechatEmoji(userMessage)
      await chatHistoryServiceClient.addUserMessage(chat_id, userMessage)
      if (userMessage === '【表情】') {
        return
      } else if (userMessage === '【语音/视频通话】') {
        await HumanTransfer.transfer(chat_id, user_id, HumanTransferType.VoiceOrVideoCall, 'onlyNotify')
        await sendMsg(user_id, chat_id, '你好，我这边在忙不方便接听电话，你要是有什么事情可以直接发消息哈，我微信可以回复消息的', '客户发起语音/视频通话')
        return
      } else if (userMessage.toLowerCase() === 'clear' && Config.isTestAccount()) {
        await this.resetChat(chat_id, user_id)
        return
      }

      const lock = new AsyncLock()

      await lock.acquire(chat_id, async () => { // 如果有新消息，当前回复会被丢弃
        const entryNode = (await chatStateStoreClient.get(chat_id)).nextStage
        const state = await getState(chat_id, user_id, userMessage) // 如果有新消息，在锁释放后，当前流程会中断
        await Workflow.run(entryNode as Node, state)
      }, { timeout: 2 * 60 * 1000 })
    } catch (e) {
      if (!(e instanceof InterruptError)) {
        try {
          if (e instanceof Error) {
            // 将错误的堆栈信息打印到自定义 logger 中
            logger.error(`Error: ${e.message}\nStack Trace: ${e.stack}`)
          } else {
            // 如果 e 不是 Error 实例，捕获当前的堆栈信息
            const stack = new Error().stack
            logger.error(`消息回复失败: ${stack}`)
          }
          await HumanTransfer.transfer(chat_id, user_id, HumanTransferType.MessageSendFailed)
        } catch (e) {
          logger.error('转人工失败', e)
        }
      }
    }
  }

  private static async run (entryNode: Node, state: IWorkflowState) {
    let node = NodeMap[entryNode]
    logger.trace({ chat_id: state.chat_id }, `初始跳转节点: ${entryNode}`)
    if (!node) {
      logger.error(`[YuheFlow] node not found: ${entryNode}`)
      return
    }

    await this.preReply(state)

    // 根据客户消息自动转移
    const autoTransferNode = await Router.route(state)
    if (autoTransferNode === Node.DummyEnd) {
      return
    }

    if (autoTransferNode && autoTransferNode !== Node.Dummy) {
      logger.trace({ chat_id: state.chat_id }, `重定向到节点: ${autoTransferNode}`)

      node = NodeMap[autoTransferNode]
      if (!node) {
        logger.error(`[YuheFlow] auto transfer node not found: ${autoTransferNode}`)
        return
      }
    }

    if (autoTransferNode !== Node.Homework1 && autoTransferNode !== Node.Homework2) { // 作业未处理完，将消息进行延迟处理，等待作业处理结束后处理
      if (Homework1Store.getUserMessages(state.chat_id).length > 0 || Homework2Store.getUserMessages(state.chat_id).length > 0) {
        HomeworkDurationUserMessageStore.addUserMessage(state.chat_id, state.userMessage)
        await sleep(3 * 60 * 1000) // 1分钟后再进行处理
        // 把当前消息挪到最后作为新消息处理，防止受到作业的影响
        const userMessages = HomeworkDurationUserMessageStore.getUserMessages(state.chat_id)
        if (userMessages.length > 0 && userMessages[userMessages.length - 1] != state.userMessage) {
          return
        }
        HomeworkDurationUserMessageStore.clearUserMessages(state.chat_id)
        setTimeout(() => {
          HomeworkDurationUserMessageStore.clearUserMessages(state.chat_id)
        }, 1000 * 60 * 3)
        for (const message of userMessages) {
          await chatHistoryServiceClient.moveToEnd(state.chat_id, message)
        }

      }
    }

    const nextStage = await node.invoke(state)
    await chatStateStoreClient.update(state.chat_id, { nextStage })

    await this.postReply(state)
  }

  private static async postReply(state: IWorkflowState) {

  }

  public static async humanInvolveGroupNotify(contactName: string, courseNo: number | null, userMessage: string) {
    await GroupNotification.notify(`${contactName}（${courseNo}）客户AI未开启，请人工处理\n客户：${userMessage}`)
  }

  private static transferWechatEmoji(message: string) {
    const emojiRegex = /\[.*?\]/g
    const emojiMap:Record<string, string> = {
      '[微笑]': '😊',
      '[调皮]': '😝',
      '[合十]': '🙏',
      '[爱心]': '💗',
      '[玫瑰]': '🌹',
      '[捂脸]': '🤦',
      '[笑哭]': '😂',
      '[咖啡]': '☕️',
      '[抱拳]': '🙏',
      '[拥抱]': '🫂'
    }
    return message.replace(emojiRegex, (match) => {
      const emoji = emojiMap[match]
      return emoji || match
    })
  }

  private static async preReply(state: IWorkflowState) {
    // 异步提取 Memory, 客户槽位
    memoryStoreClient.pushRecentMemoryToVectorDB(state.chat_id, state.round_id, new ExtractUserSlots())
  }

  private static async resetChat(chat_id: string, user_id: string) {
    await wecomMessageSender.sendById({ user_id, chat_id: chat_id, ai_msg: '聊天已重置' })
    await chatHistoryServiceClient.clearChatHistory(chat_id)
    await chatStateStoreClient.clear(chat_id)
    if (await chatDBClient.getById(chat_id)) {
      await chatDBClient.setHumanInvolvement(chat_id, false)
    }

    await VisualizedSopTasks.clearSop('yuhe', chat_id)

    // clear memory
    await memoryStoreClient.clearMemory(chat_id)

    await sendYuHeWelComeMessage(chat_id, user_id)
  }
}