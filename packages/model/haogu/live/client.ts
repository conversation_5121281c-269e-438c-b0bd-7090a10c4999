import axios, { AxiosInstance, AxiosError } from 'axios'
import type {
  ApiResponse,
  GetTokenPayload,
  ApiColumnsRes,
  ApiLiveListRes,
  ApiGoodsDetailRes,
  ApiProductPushRes,
  ApiBookListRes,
  QueryUserWatchListPayload,
  ApiQueryUserWatchListRes,
  QueryLiveMessagePagePayload,
  ApiQueryLiveMessagePageRes,
  ProductPushPayload,
} from './type'
import { RedisDB } from '../../redis/redis'
import logger from '../../logger/logger'

/**
 * LiveApi client generated from provided doc
 * - supports test/prod base urls
 * - add Authorization header when token is set via setToken
 */
export class LiveApi {
  private client: AxiosInstance
  private appId:string
  private appSecret:string

  static tokenRedisKeyPrefix:string = 'haogu:live_token'

  constructor(baseUrl: string, appId:string, appSecret:string, timeout = 10000) {
    this.client = axios.create({ baseURL: baseUrl, timeout })
    this.appId = appId
    this.appSecret = appSecret
    this.client.interceptors.request.use(async (config) => {
      if (config.url != 'ai/live/getToken') {
        const redisClient = RedisDB.getInstance()
        const redisKey = `${LiveApi.tokenRedisKeyPrefix}:${appId}`
        let token = await redisClient.get(redisKey)
        if (!token) {
          token = await this.getToken({
            appId:this.appId,
            appSecret:this.appSecret
          })
          if (token) {
            await redisClient.set(redisKey, token)
            await redisClient.expire(redisKey, 60 * 60 * 24 * 20) // 20天
          }
        }
        if (token) {
          config.headers['Authorization'] = token
        }
      }
      config.headers['Content-Type'] = 'application/json;charset=utf-8'
      return config
    })
  }

  // 1. 获取 token
  public async getToken(payload: GetTokenPayload): Promise<string | null> {
    const path = 'ai/live/getToken'
    try {
      const response = await this.client.post<ApiResponse<string>>(path, payload)
      return response.data.data
    } catch (err) {
      if (err instanceof AxiosError) {
        logger.error(`请求接口${path}错误`, err.message, err.response?.data, payload)
      } else {
        logger.error(`请求接口${path}错误`, err)
      }
      return null
    }
  }

  // 2. 获取专栏列表
  public async getColumns(): Promise<ApiColumnsRes | null> {
    const path = 'ai/live/getColumns'
    try {
      const response = await this.client.get<ApiResponse<ApiColumnsRes>>(path)
      return response.data.data
    } catch (err) {
      if (err instanceof AxiosError) {
        logger.error(`请求接口${path}错误`, err.message, err.response?.data)
      } else {
        logger.error(`请求接口${path}错误`, err)
      }
      return null
    }
  }

  // 3. 获取直播列表（根据专栏）
  public async getLiveByCourseId(courseId: string): Promise<ApiLiveListRes | null> {
    const path = 'ai/live/getLiveByCourseId'
    try {
      const response = await this.client.get<ApiResponse<ApiLiveListRes>>(path, { params: { courseId } })
      return response.data.data
    } catch (err) {
      if (err instanceof AxiosError) {
        logger.error(`请求接口${path}错误`, err.message, err.response?.data, { courseId })
      } else {
        logger.error(`请求接口${path}错误`, err)
      }
      return null
    }
  }

  // 4. 商品用户订单详情
  public async queryLiveGoodsDetails(channelId: string, productId: number): Promise<ApiGoodsDetailRes | null> {
    const path = 'ai/live/queryLiveGoodsDetails'
    try {
      const response = await this.client.get<ApiResponse<ApiGoodsDetailRes>>(path, { params: { channelId, productId } })
      return response.data.data
    } catch (err) {
      if (err instanceof AxiosError) {
        logger.error(`请求接口${path}错误`, err.message, err.response?.data, { channelId, productId })
      } else {
        logger.error(`请求接口${path}错误`, err)
      }
      return null
    }
  }

  // 5. 商品推送
  public async productPush(payload: ProductPushPayload): Promise<ApiProductPushRes | null> {
    const path = 'ai/live/productPush'
    try {
      const response = await this.client.post<ApiResponse<ApiProductPushRes>>(path, payload)
      return response.data.data
    } catch (err) {
      if (err instanceof AxiosError) {
        logger.error(`请求接口${path}错误`, err.message, err.response?.data, payload)
      } else {
        logger.error(`请求接口${path}错误`, err)
      }
      return null
    }
  }

  // 6. 查询直播预约数据
  public async getBookByLiveId(payload: { liveId: string; unionId?: string }): Promise<ApiBookListRes | null> {
    const path = 'ai/live/getBookByLiveId'
    try {
      const response = await this.client.post<ApiResponse<ApiBookListRes>>(path, payload)
      return response.data.data
    } catch (err) {
      if (err instanceof AxiosError) {
        logger.error(`请求接口${path}错误`, err.message, err.response?.data, payload)
      } else {
        logger.error(`请求接口${path}错误`, err)
      }
      return null
    }
  }

  // 7. 查询直播观看数据
  public async queryUserWatchList(payload: QueryUserWatchListPayload): Promise<ApiQueryUserWatchListRes | null> {
    const path = 'ai/live/queryUserWatchList'
    try {
      const response = await this.client.post<ApiResponse<ApiQueryUserWatchListRes>>(path, payload)
      return response.data.data
    } catch (err) {
      if (err instanceof AxiosError) {
        logger.error(`请求接口${path}错误`, err.message, err.response?.data, payload)
      } else {
        logger.error(`请求接口${path}错误`, err)
      }
      return null
    }
  }

  // 8. 查询直播聊天数据
  public async queryLiveMessagePage(payload: QueryLiveMessagePagePayload): Promise<ApiQueryLiveMessagePageRes | null> {
    const path = 'ai/live/queryLiveMessagePage'
    try {
      const response = await this.client.post<ApiResponse<ApiQueryLiveMessagePageRes>>(path, payload)
      return response.data.data
    } catch (err) {
      if (err instanceof AxiosError) {
        logger.error(`请求接口${path}错误`, err.message, err.response?.data, payload)
      } else {
        logger.error(`请求接口${path}错误`, err)
      }
      return null
    }
  }
}

export default LiveApi
