import axios, { AxiosInstance, AxiosError } from 'axios'
import type {
  ApiResponse,
  GetTokenPayload,
  ApiColumnsRes,
  ApiLiveListRes,
  ApiGoodsDetailRes,
  ApiProductPushRes,
  ApiBookListRes,
  QueryUserWatchListPayload,
  ApiQueryUserWatchListRes,
  QueryLiveMessagePagePayload,
  ApiQueryLiveMessagePageRes,
  ProductPushPayload,
} from './type'
import { RedisDB } from '../../redis/redis'
import logger from '../../logger/logger'

/**
 * LiveApi client generated from provided doc
 * - supports test/prod base urls
 * - add Authorization header when token is set via setToken
 */
export class LiveApi {
  private client: AxiosInstance
  private appId:string
  private appSecret:string

  static tokenRedisKeyPrefix:string = 'haogu:live_token'

  constructor(baseUrl: string, appId:string, appSecret:string, timeout = 10000) {
    this.client = axios.create({ baseURL: baseUrl, timeout })
    this.appId = appId
    this.appSecret = appSecret
    this.client.interceptors.request.use(async (config) => {
      if (config.url != 'ai/live/getToken') {
        const redisClient = RedisDB.getInstance()
        const redisKey = `${LiveApi.tokenRedisKeyPrefix}:${appId}`
        let token = await redisClient.get(redisKey)
        if (!token) {
          token = await this.getToken({
            appId:this.appId,
            appSecret:this.appSecret
          })
          if (token) {
            await redisClient.set(redisKey, token)
            await redisClient.expire(redisKey, 60 * 60 * 24 * 20) // 20天
          }
        }
        if (token) {
          config.headers['Authorization'] = token
        }
      }
      config.headers['Content-Type'] = 'application/json;charset=utf-8'
      return config
    })
  }

  /**
   * 统一处理API响应和错误
   */
  private _handleApiResponse<T>(response: any, path: string, payload?: any): T | null {
    const { code, msg, data } = response.data

    if (code && code === 0) {
      return data
    } else {
      logger.error(`请求接口${path}失败`, { code, msg, payload })
      return null
    }
  }

  /**
   * 统一的GET请求方法
   */
  private async _get<T>(path: string, params?: any): Promise<T | null> {
    try {
      const response = await this.client.get<ApiResponse<T>>(path, { params })
      return this._handleApiResponse<T>(response, path, params)
    } catch (err) {
      if (err instanceof AxiosError) {
        logger.error(`请求接口${path}错误`, err.message, err.response?.data, params)
      } else {
        logger.error(`请求接口${path}错误`, err)
      }
      return null
    }
  }

  /**
   * 统一的POST请求方法
   */
  private async _post<T>(path: string, payload?: any): Promise<T | null> {
    try {
      const response = await this.client.post<ApiResponse<T>>(path, payload)
      return this._handleApiResponse<T>(response, path, payload)
    } catch (err) {
      if (err instanceof AxiosError) {
        logger.error(`请求接口${path}错误`, err.message, err.response?.data, payload)
      } else {
        logger.error(`请求接口${path}错误`, err)
      }
      return null
    }
  }

  // 1. 获取 token
  public async getToken(payload: GetTokenPayload): Promise<string | null> {
    return this._post<string>('ai/live/getToken', payload)
  }

  // 2. 获取专栏列表
  public async getColumns(): Promise<ApiColumnsRes | null> {
    return this._get<ApiColumnsRes>('ai/live/getColumns')
  }

  // 3. 获取直播列表（根据专栏）
  public async getLiveByCourseId(courseId: string): Promise<ApiLiveListRes | null> {
    return this._get<ApiLiveListRes>('ai/live/getLiveByCourseId', { courseId })
  }

  // 4. 商品用户订单详情
  public async queryLiveGoodsDetails(channelId: string, productId: number): Promise<ApiGoodsDetailRes | null> {
    return this._get<ApiGoodsDetailRes>('ai/live/queryLiveGoodsDetails', { channelId, productId })
  }

  // 5. 商品推送
  public async productPush(payload: ProductPushPayload): Promise<ApiProductPushRes | null> {
    return this._post<ApiProductPushRes>('ai/live/productPush', payload)
  }

  // 6. 查询直播预约数据
  public async getBookByLiveId(payload: { liveId: string; unionId?: string }): Promise<ApiBookListRes | null> {
    return this._post<ApiBookListRes>('ai/live/getBookByLiveId', payload)
  }

  // 7. 查询直播观看数据
  public async queryUserWatchList(payload: QueryUserWatchListPayload): Promise<ApiQueryUserWatchListRes | null> {
    return this._post<ApiQueryUserWatchListRes>('ai/live/queryUserWatchList', payload)
  }

  // 8. 查询直播聊天数据
  public async queryLiveMessagePage(payload: QueryLiveMessagePagePayload): Promise<ApiQueryLiveMessagePageRes | null> {
    return this._post<ApiQueryLiveMessagePageRes>('ai/live/queryLiveMessagePage', payload)
  }
}

export default LiveApi
