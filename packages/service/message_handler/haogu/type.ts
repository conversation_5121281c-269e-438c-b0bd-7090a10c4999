export enum HaoguMessageType {
  text = 'text',
  image = 'image',
  emotion = 'emotion',
  video = 'video',
  file = 'file',
  link = 'link',
  videoChannel = 'videoChannel',
  media = 'media'
}

export type HaoguTextMessage = {
  text:string
}

export type HaoguImageMessage = {
  fileUrl:string
  fileSize:number
  imageWidth:number
  imageHeight:number
  md5:string
}

export type HaoguEmotionMessage = {
  fileUrl:string
  type:EmotionType
}

export enum EmotionType {
  static = 1,
  dynamic = 2
}

export type HaoguVideoMessage = {
  fileUrl:string
  fileSize:number
  imageWidth:number
  imageHeight:number
  md5:string
}

export type HaoguFileMessage = {
  fileUrl:string
  fileSize:number
  md5:string
}

export type HaoguLinkMessage = {
  title:string
  desc:string
  url:string
  imageUrl:string
}

export type HaoguVideoChannelMessage = {
  mpvVideoId:string
}

export type HaoguMediaMessage = {
  fileUrl:string
  fileSourceUrl:string
  fileTime:number
  md5:string
}

export type HaoguCanSendMessage = HaoguTextMessage | HaoguImageMessage | HaoguEmotionMessage | HaoguVideoMessage | HaoguFileMessage | HaoguLinkMessage | HaoguVideoChannelMessage | HaoguMediaMessage