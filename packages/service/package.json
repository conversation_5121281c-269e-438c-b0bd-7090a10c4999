{"name": "service", "private": true, "scripts": {"tsc-check": "tsc --noEmit"}, "devDependencies": {"@types/jest": "^30.0.0", "@types/node": "^20.7.0", "typescript": "5.8.2"}, "dependencies": {"@elastic/elasticsearch": "^8.13.1", "@langchain/community": "^0.3.50", "@langchain/core": "^0.3.70", "@ycloud-cpaas/ycloud-sdk-node": "^1.15.5", "async-sema": "^3.1.1", "axios": "^1.5.1", "bullmq": "^5.12.9", "chalk": "^4.1.2", "cheerio": "^1.0.0-rc.12", "config": "workspace:*", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "fast-xml-parser": "^4.3.5", "ioredis": "^5.4.1", "langchain": "^0.3.21", "lib": "workspace:*", "lru-cache": "^10.2.0", "markdown-to-text": "^0.1.1", "model": "workspace:*", "openai": "^4.98.0", "pickleparser": "^0.2.1", "zod": "^3.23.8"}, "packageManager": "pnpm@9.0.0", "engines": {"node": ">=18"}}